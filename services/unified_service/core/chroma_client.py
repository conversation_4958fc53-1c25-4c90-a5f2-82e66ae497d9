"""
ChromaDB客户端
连接到独立的ChromaDB服务，用于查询向量数据
"""

import httpx
from typing import Optional, List, Dict, Any
from shared.utils.logger import get_logger
from services.unified_service.config import config

logger = get_logger(__name__)


class ChromaClient:
    """ChromaDB客户端"""
    
    def __init__(self):
        self.base_url = config.chroma_url
        self.collection_prefix = config.chroma_collection_prefix
        self.http_client = httpx.AsyncClient(timeout=30.0)
        
        logger.info(f"ChromaDB客户端初始化完成，服务地址: {self.base_url}")
    
    def _get_collection_name(self, file_code: str) -> str:
        """
        获取集合名称
        
        Args:
            file_code: 文件编码
            
        Returns:
            集合名称
        """
        return f"{self.collection_prefix}{file_code}"
    
    async def check_file_exists(self, file_code: str) -> bool:
        """
        检查文件是否存在于向量数据库中
        
        Args:
            file_code: 文件编码
            
        Returns:
            是否存在
        """
        try:
            collection_name = self._get_collection_name(file_code)
            
            # 查询集合是否存在
            response = await self.http_client.get(
                f"{self.base_url}/api/v1/collections/{collection_name}"
            )
            
            if response.status_code == 200:
                # 检查集合是否有数据
                count_response = await self.http_client.get(
                    f"{self.base_url}/api/v1/collections/{collection_name}/count"
                )
                
                if count_response.status_code == 200:
                    count_data = count_response.json()
                    document_count = count_data.get("count", 0)
                    
                    logger.debug(f"文件 {file_code} 存在，文档数量: {document_count}")
                    return document_count > 0
                
            return False
            
        except Exception as e:
            logger.error(f"检查文件存在性失败 {file_code}: {e}")
            return False
    
    async def get_file_info(self, file_code: str) -> Optional[Dict[str, Any]]:
        """
        获取文件信息
        
        Args:
            file_code: 文件编码
            
        Returns:
            文件信息字典，如果不存在返回None
        """
        try:
            collection_name = self._get_collection_name(file_code)
            
            # 获取集合信息
            response = await self.http_client.get(
                f"{self.base_url}/api/v1/collections/{collection_name}"
            )
            
            if response.status_code != 200:
                return None
            
            collection_info = response.json()
            
            # 获取文档数量
            count_response = await self.http_client.get(
                f"{self.base_url}/api/v1/collections/{collection_name}/count"
            )
            
            document_count = 0
            if count_response.status_code == 200:
                count_data = count_response.json()
                document_count = count_data.get("count", 0)
            
            return {
                "file_code": file_code,
                "collection_name": collection_name,
                "document_count": document_count,
                "metadata": collection_info.get("metadata", {}),
                "created_at": collection_info.get("created_at"),
            }
            
        except Exception as e:
            logger.error(f"获取文件信息失败 {file_code}: {e}")
            return None
    
    async def query_documents(
        self, 
        file_code: str, 
        query_text: str, 
        n_results: int = 5
    ) -> List[Dict[str, Any]]:
        """
        查询文档
        
        Args:
            file_code: 文件编码
            query_text: 查询文本
            n_results: 返回结果数量
            
        Returns:
            查询结果列表
        """
        try:
            collection_name = self._get_collection_name(file_code)
            
            # 构建查询请求
            query_data = {
                "query_texts": [query_text],
                "n_results": n_results,
                "include": ["documents", "metadatas", "distances"]
            }
            
            response = await self.http_client.post(
                f"{self.base_url}/api/v1/collections/{collection_name}/query",
                json=query_data
            )
            
            if response.status_code != 200:
                logger.error(f"查询文档失败 {file_code}: HTTP {response.status_code}")
                return []
            
            result_data = response.json()
            
            # 解析查询结果
            documents = result_data.get("documents", [[]])[0]
            metadatas = result_data.get("metadatas", [[]])[0]
            distances = result_data.get("distances", [[]])[0]
            
            results = []
            for i in range(len(documents)):
                results.append({
                    "document": documents[i],
                    "metadata": metadatas[i] if i < len(metadatas) else {},
                    "distance": distances[i] if i < len(distances) else 0.0,
                })
            
            logger.debug(f"查询文档成功 {file_code}: 返回 {len(results)} 个结果")
            return results
            
        except Exception as e:
            logger.error(f"查询文档失败 {file_code}: {e}")
            return []
    
    async def get_all_files(self) -> List[Dict[str, Any]]:
        """
        获取所有文件列表
        
        Returns:
            文件列表
        """
        try:
            # 获取所有集合
            response = await self.http_client.get(
                f"{self.base_url}/api/v1/collections"
            )
            
            if response.status_code != 200:
                logger.error(f"获取集合列表失败: HTTP {response.status_code}")
                return []
            
            collections = response.json()
            files = []
            
            for collection in collections:
                collection_name = collection.get("name", "")
                
                # 只处理以指定前缀开头的集合
                if collection_name.startswith(self.collection_prefix):
                    file_code = collection_name[len(self.collection_prefix):]
                    
                    # 获取文档数量
                    count_response = await self.http_client.get(
                        f"{self.base_url}/api/v1/collections/{collection_name}/count"
                    )
                    
                    document_count = 0
                    if count_response.status_code == 200:
                        count_data = count_response.json()
                        document_count = count_data.get("count", 0)
                    
                    files.append({
                        "file_code": file_code,
                        "collection_name": collection_name,
                        "document_count": document_count,
                        "metadata": collection.get("metadata", {}),
                    })
            
            logger.debug(f"获取文件列表成功: {len(files)} 个文件")
            return files
            
        except Exception as e:
            logger.error(f"获取文件列表失败: {e}")
            return []
    
    async def delete_file(self, file_code: str) -> bool:
        """
        删除文件的向量数据
        
        Args:
            file_code: 文件编码
            
        Returns:
            是否成功删除
        """
        try:
            collection_name = self._get_collection_name(file_code)
            
            response = await self.http_client.delete(
                f"{self.base_url}/api/v1/collections/{collection_name}"
            )
            
            if response.status_code == 200:
                logger.info(f"删除文件向量数据成功: {file_code}")
                return True
            else:
                logger.error(f"删除文件向量数据失败 {file_code}: HTTP {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"删除文件向量数据失败 {file_code}: {e}")
            return False
    
    async def close(self):
        """关闭客户端"""
        await self.http_client.aclose()
