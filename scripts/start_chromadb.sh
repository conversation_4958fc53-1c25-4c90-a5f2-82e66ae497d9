#!/bin/bash

# ChromaDB服务启动脚本

set -e

echo "🚀 启动ChromaDB服务..."

# 检查Python环境
if ! command -v python &> /dev/null; then
    echo "❌ Python未找到，请先安装Python"
    exit 1
fi

# 检查ChromaDB是否已安装
python -c "import chromadb" 2>/dev/null || {
    echo "❌ ChromaDB未安装，正在安装..."
    pip install chromadb
}

# 设置环境变量（如果未设置）
export CHROMA_HOST=${CHROMA_HOST:-"0.0.0.0"}
export CHROMA_PORT=${CHROMA_PORT:-"8001"}
export CHROMA_PERSIST_DIR=${CHROMA_PERSIST_DIR:-"./data/chromadb"}
export LOG_LEVEL=${LOG_LEVEL:-"INFO"}

echo "📡 ChromaDB服务配置:"
echo "  - 主机: $CHROMA_HOST"
echo "  - 端口: $CHROMA_PORT"
echo "  - 数据目录: $CHROMA_PERSIST_DIR"
echo "  - 日志级别: $LOG_LEVEL"

# 创建数据目录
mkdir -p "$CHROMA_PERSIST_DIR"

echo "📁 数据目录已准备: $CHROMA_PERSIST_DIR"

# 检查端口是否被占用
if lsof -Pi :$CHROMA_PORT -sTCP:LISTEN -t >/dev/null ; then
    echo "⚠️  端口 $CHROMA_PORT 已被占用"
    echo "正在尝试停止现有服务..."
    
    # 尝试优雅停止
    pkill -f "chromadb.*$CHROMA_PORT" || true
    sleep 2
    
    # 强制停止
    if lsof -Pi :$CHROMA_PORT -sTCP:LISTEN -t >/dev/null ; then
        echo "强制停止占用端口的进程..."
        lsof -ti:$CHROMA_PORT | xargs kill -9 || true
        sleep 1
    fi
fi

echo "✅ 端口 $CHROMA_PORT 可用"

# 启动ChromaDB服务
echo "🎯 启动ChromaDB服务..."

# 方法1: 使用我们的自定义服务器
if [ -f "services/chromadb_service/server.py" ]; then
    echo "使用自定义ChromaDB服务器..."
    python services/chromadb_service/server.py
else
    # 方法2: 使用ChromaDB内置服务器
    echo "使用ChromaDB内置服务器..."
    chroma run --host $CHROMA_HOST --port $CHROMA_PORT --path $CHROMA_PERSIST_DIR
fi
