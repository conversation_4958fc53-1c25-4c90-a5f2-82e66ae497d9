"""
Worker服务配置
专门处理BGE-M3向量化和风险分析任务
"""

import os
from pathlib import Path
from typing import Optional
from pydantic_settings import BaseSettings


class WorkerServiceConfig(BaseSettings):
    """Worker服务配置"""

    # 服务基本信息
    service_name: str = "worker-service"
    service_version: str = "1.0.0"
    log_level: str = os.getenv("LOG_LEVEL", "INFO")

    # BGE-M3模型配置 - 使用本地模型路径
    model_name: str = os.getenv("MODEL_NAME", str(Path(__file__).parent.parent.parent / "models" / "bge-m3-safetensors-only"))
    model_cache_dir: Optional[str] = os.getenv("MODEL_CACHE_DIR", "./models")
    device: str = os.getenv("DEVICE", "auto")  # auto, cpu, cuda
    batch_size: int = int(os.getenv("BATCH_SIZE", "32"))
    max_length: int = int(os.getenv("MAX_LENGTH", "8192"))
    normalize_embeddings: bool = True

    # ChromaDB配置 (客户端模式，连接到独立的ChromaDB服务)
    chroma_host: str = os.getenv("CHROMA_HOST", "localhost")
    chroma_port: int = int(os.getenv("CHROMA_PORT", "8001"))
    chroma_collection_prefix: str = os.getenv("CHROMA_COLLECTION_PREFIX", "file_")

    # Redis配置 (用于Celery)
    redis_host: str = os.getenv("REDIS_HOST", "localhost")
    redis_port: int = int(os.getenv("REDIS_PORT", "6379"))
    redis_db: int = int(os.getenv("REDIS_DB", "0"))
    redis_password: Optional[str] = os.getenv("REDIS_PASSWORD", None)

    # LLM配置 (用于风险分析)
    llm_provider: str = os.getenv("LLM_PROVIDER", "deepseek")
    deepseek_api_key: str = os.getenv("DEEPSEEK_API_KEY", "")
    deepseek_model: str = os.getenv("DEEPSEEK_MODEL", "deepseek-reasoner")
    openai_api_key: str = os.getenv("OPENAI_API_KEY", "")
    openai_model: str = os.getenv("OPENAI_MODEL", "gpt-4")
    enable_streaming: bool = os.getenv("ENABLE_STREAMING", "true").lower() == "true"

    # 提示词配置
    prompts_dir: str = os.getenv("PROMPTS_DIR", "./prompts")

    # 文件处理配置
    chunk_size: int = int(os.getenv("CHUNK_SIZE", "1000"))
    chunk_overlap: int = int(os.getenv("CHUNK_OVERLAP", "200"))
    max_chunks_per_file: int = int(os.getenv("MAX_CHUNKS_PER_FILE", "100"))

    # 任务配置
    task_timeout: int = int(os.getenv("TASK_TIMEOUT", "1800"))  # 30分钟
    task_soft_timeout: int = int(os.getenv("TASK_SOFT_TIMEOUT", "1500"))  # 25分钟
    max_retries: int = int(os.getenv("MAX_RETRIES", "3"))
    retry_delay: int = int(os.getenv("RETRY_DELAY", "60"))  # 60秒

    # 内存管理配置
    enable_memory_monitoring: bool = os.getenv("ENABLE_MEMORY_MONITORING", "true").lower() == "true"
    memory_cleanup_threshold: float = float(os.getenv("MEMORY_CLEANUP_THRESHOLD", "80.0"))  # 80%
    auto_cleanup: bool = os.getenv("AUTO_CLEANUP", "true").lower() == "true"

    @property
    def chroma_url(self) -> str:
        """ChromaDB服务URL"""
        return f"http://{self.chroma_host}:{self.chroma_port}"

    @property
    def redis_url(self) -> str:
        """Redis连接URL"""
        if self.redis_password:
            return f"redis://:{self.redis_password}@{self.redis_host}:{self.redis_port}/{self.redis_db}"
        else:
            return f"redis://{self.redis_host}:{self.redis_port}/{self.redis_db}"

    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"


# 创建全局配置实例
config = WorkerServiceConfig()
