version: '3.8'

services:
  # Redis (消息队列和缓存)
  redis:
    image: redis:7-alpine
    container_name: deep-risk-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    networks:
      - deep-risk-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 3s
      retries: 3

  # ChromaDB 独立服务
  chromadb:
    image: chromadb/chroma:latest
    container_name: deep-risk-chromadb
    ports:
      - "8001:8000"
    volumes:
      - chromadb_data:/chroma/chroma
    environment:
      - CHROMA_SERVER_HOST=0.0.0.0
      - CHROMA_SERVER_HTTP_PORT=8000
      - ANONYMIZED_TELEMETRY=False
      - ALLOW_RESET=true
    networks:
      - deep-risk-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/api/v1/heartbeat"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 统一Web服务 (合并原有分析和向量化API)
  unified-service:
    build:
      context: ..
      dockerfile: docker/unified-service.Dockerfile
    container_name: deep-risk-unified-service
    ports:
      - "8000:8000"
    volumes:
      - ../data:/app/data
      - ../prompts:/app/prompts
    environment:
      - SERVICE_NAME=unified-service
      - HOST=0.0.0.0
      - PORT=8000
      - LOG_LEVEL=INFO
      # ChromaDB配置
      - CHROMA_HOST=chromadb
      - CHROMA_PORT=8000
      # Redis配置
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_DB=0
      # LLM配置
      - LLM_PROVIDER=deepseek
      - DEEPSEEK_API_KEY=${DEEPSEEK_API_KEY}
      - DEEPSEEK_MODEL=${DEEPSEEK_MODEL:-deepseek-reasoner}
      - ENABLE_STREAMING=true
      # 文件上传配置
      - UPLOAD_DIR=/app/data/uploads
      - MAX_FILE_SIZE=100
      # 任务配置
      - TASK_TIMEOUT=1800
      - TASK_RETRY_MAX=3
    depends_on:
      redis:
        condition: service_healthy
      chromadb:
        condition: service_healthy
    networks:
      - deep-risk-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Celery Worker服务 (GPU部署，处理BGE-M3向量化)
  worker-service:
    build:
      context: ..
      dockerfile: docker/worker-service.Dockerfile
    container_name: deep-risk-worker-service
    volumes:
      - ../models:/app/models
      - ../data:/app/data
      - ../prompts:/app/prompts
    environment:
      - SERVICE_NAME=worker-service
      - LOG_LEVEL=INFO
      # BGE-M3模型配置
      - MODEL_NAME=/app/models/bge-m3-safetensors-only
      - DEVICE=auto
      - BATCH_SIZE=32
      - MAX_LENGTH=8192
      # ChromaDB配置
      - CHROMA_HOST=chromadb
      - CHROMA_PORT=8000
      # Redis配置
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_DB=0
      # LLM配置
      - LLM_PROVIDER=deepseek
      - DEEPSEEK_API_KEY=${DEEPSEEK_API_KEY}
      - DEEPSEEK_MODEL=${DEEPSEEK_MODEL:-deepseek-reasoner}
      # 任务配置
      - TASK_TIMEOUT=1800
      - MAX_RETRIES=3
      - CHUNK_SIZE=1000
      - CHUNK_OVERLAP=200
      # 内存管理
      - ENABLE_MEMORY_MONITORING=true
      - AUTO_CLEANUP=true
      - MEMORY_CLEANUP_THRESHOLD=80.0
      - CUDA_VISIBLE_DEVICES=0  # 指定GPU设备
    depends_on:
      redis:
        condition: service_healthy
      chromadb:
        condition: service_healthy
    networks:
      - deep-risk-network
    restart: unless-stopped
    # GPU支持 (需要nvidia-docker)
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]

  # Flower监控 (Celery任务监控)
  flower:
    image: mher/flower:0.9.7
    container_name: deep-risk-flower
    ports:
      - "5555:5555"
    environment:
      - CELERY_BROKER_URL=redis://redis:6379/0
      - FLOWER_PORT=5555
    depends_on:
      redis:
        condition: service_healthy
    networks:
      - deep-risk-network
    restart: unless-stopped



networks:
  deep-risk-network:
    driver: bridge

volumes:
  redis_data:
    driver: local
  chromadb_data:
    driver: local
