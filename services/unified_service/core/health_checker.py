"""
统一服务健康检查器
检查所有依赖组件的健康状态
"""

import asyncio
from typing import Dict, bool
import redis
import httpx
from shared.utils.logger import get_logger
from services.unified_service.config import config

logger = get_logger(__name__)


class HealthChecker:
    """统一服务健康检查器"""
    
    def __init__(self):
        self.redis_client = None
        self.http_client = httpx.AsyncClient(timeout=5.0)
    
    async def check_redis(self) -> bool:
        """检查Redis连接"""
        try:
            if not self.redis_client:
                self.redis_client = redis.Redis.from_url(config.redis_url)
            
            # 测试连接
            await asyncio.get_event_loop().run_in_executor(
                None, self.redis_client.ping
            )
            logger.debug("Redis健康检查通过")
            return True
            
        except Exception as e:
            logger.warning(f"Redis健康检查失败: {e}")
            return False
    
    async def check_chromadb(self) -> bool:
        """检查ChromaDB服务"""
        try:
            # 检查ChromaDB心跳接口
            response = await self.http_client.get(
                f"{config.chroma_url}/api/v1/heartbeat"
            )
            
            if response.status_code == 200:
                logger.debug("ChromaDB健康检查通过")
                return True
            else:
                logger.warning(f"ChromaDB健康检查失败: HTTP {response.status_code}")
                return False
                
        except Exception as e:
            logger.warning(f"ChromaDB健康检查失败: {e}")
            return False
    
    async def check_celery_broker(self) -> bool:
        """检查Celery消息队列"""
        try:
            # 通过Redis检查Celery broker
            if not self.redis_client:
                self.redis_client = redis.Redis.from_url(config.redis_url)
            
            # 检查Celery相关的键
            await asyncio.get_event_loop().run_in_executor(
                None, self.redis_client.ping
            )
            
            logger.debug("Celery broker健康检查通过")
            return True
            
        except Exception as e:
            logger.warning(f"Celery broker健康检查失败: {e}")
            return False
    
    async def check_llm_service(self) -> bool:
        """检查LLM服务可用性"""
        try:
            # 根据配置的LLM提供商进行检查
            if config.llm_provider == "deepseek":
                return await self._check_deepseek()
            elif config.llm_provider == "openai":
                return await self._check_openai()
            else:
                logger.warning(f"未知的LLM提供商: {config.llm_provider}")
                return False
                
        except Exception as e:
            logger.warning(f"LLM服务健康检查失败: {e}")
            return False
    
    async def _check_deepseek(self) -> bool:
        """检查DeepSeek API"""
        try:
            if not config.deepseek_api_key:
                logger.warning("DeepSeek API密钥未配置")
                return False
            
            # 简单的API可用性检查
            headers = {
                "Authorization": f"Bearer {config.deepseek_api_key}",
                "Content-Type": "application/json"
            }
            
            # 发送一个简单的请求来测试API
            response = await self.http_client.post(
                "https://api.deepseek.com/v1/chat/completions",
                headers=headers,
                json={
                    "model": config.deepseek_model,
                    "messages": [{"role": "user", "content": "test"}],
                    "max_tokens": 1
                }
            )
            
            # 检查响应状态
            if response.status_code in [200, 400]:  # 400也可能表示API可用但参数错误
                logger.debug("DeepSeek API健康检查通过")
                return True
            else:
                logger.warning(f"DeepSeek API健康检查失败: HTTP {response.status_code}")
                return False
                
        except Exception as e:
            logger.warning(f"DeepSeek API健康检查失败: {e}")
            return False
    
    async def _check_openai(self) -> bool:
        """检查OpenAI API"""
        try:
            if not config.openai_api_key:
                logger.warning("OpenAI API密钥未配置")
                return False
            
            # 简单的API可用性检查
            headers = {
                "Authorization": f"Bearer {config.openai_api_key}",
                "Content-Type": "application/json"
            }
            
            response = await self.http_client.post(
                "https://api.openai.com/v1/chat/completions",
                headers=headers,
                json={
                    "model": config.openai_model,
                    "messages": [{"role": "user", "content": "test"}],
                    "max_tokens": 1
                }
            )
            
            if response.status_code in [200, 400]:
                logger.debug("OpenAI API健康检查通过")
                return True
            else:
                logger.warning(f"OpenAI API健康检查失败: HTTP {response.status_code}")
                return False
                
        except Exception as e:
            logger.warning(f"OpenAI API健康检查失败: {e}")
            return False
    
    async def check_all_components(self) -> Dict[str, bool]:
        """
        检查所有组件的健康状态
        
        Returns:
            各组件的健康状态字典
        """
        checks = {}
        
        # 并发执行所有健康检查
        tasks = {
            "redis": self.check_redis(),
            "chromadb": self.check_chromadb(),
            "celery_broker": self.check_celery_broker(),
            "llm_service": self.check_llm_service(),
        }
        
        # 等待所有检查完成
        results = await asyncio.gather(*tasks.values(), return_exceptions=True)
        
        # 处理结果
        for i, (component, task) in enumerate(tasks.items()):
            result = results[i]
            if isinstance(result, Exception):
                logger.error(f"{component}健康检查异常: {result}")
                checks[component] = False
            else:
                checks[component] = result
        
        return checks
    
    async def close(self):
        """关闭资源"""
        if self.redis_client:
            await asyncio.get_event_loop().run_in_executor(
                None, self.redis_client.close
            )
        
        await self.http_client.aclose()
