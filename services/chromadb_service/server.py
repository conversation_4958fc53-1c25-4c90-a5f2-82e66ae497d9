"""
ChromaDB服务端启动脚本
独立运行的ChromaDB HTTP服务
"""

import os
import sys
import uvicorn
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from shared.utils.logger import setup_logging, get_logger
from services.chromadb_service.config import config

# 设置日志
setup_logging(level=config.log_level, service_name=config.service_name)

logger = get_logger(__name__)


def create_chromadb_app():
    """创建ChromaDB FastAPI应用"""
    try:
        import chromadb
        from chromadb.config import Settings
        from chromadb.server.fastapi import FastAPI
        
        # ChromaDB设置
        chroma_settings = Settings(
            chroma_db_impl="duckdb+parquet",
            persist_directory=config.persist_directory,
            chroma_server_host=config.host,
            chroma_server_http_port=config.port,
            allow_reset=config.allow_reset,
            chroma_server_cors_allow_origins=config.cors_allow_origins,
        )
        
        # 如果启用认证
        if config.auth_enabled and config.auth_token:
            chroma_settings.chroma_server_auth_provider = "chromadb.auth.token.TokenAuthServerProvider"
            chroma_settings.chroma_server_auth_credentials = config.auth_token
        
        # 创建ChromaDB服务器
        server = chromadb.Server(chroma_settings)
        
        # 创建FastAPI应用
        app = FastAPI(server)
        
        logger.info("ChromaDB FastAPI应用创建成功")
        return app
        
    except ImportError as e:
        logger.error(f"ChromaDB导入失败: {e}")
        logger.error("请安装ChromaDB: pip install chromadb")
        raise
    except Exception as e:
        logger.error(f"创建ChromaDB应用失败: {e}")
        raise


def main():
    """主函数"""
    logger.info("=" * 60)
    logger.info(f"🚀 启动 {config.service_name} v{config.service_version}")
    logger.info("=" * 60)
    
    # 服务配置信息
    logger.info(f"📡 服务地址: {config.host}:{config.port}")
    logger.info(f"📝 日志级别: {config.log_level}")
    logger.info(f"💾 持久化目录: {config.persist_directory}")
    logger.info(f"🔐 认证启用: {config.auth_enabled}")
    logger.info(f"📊 最大批次大小: {config.max_batch_size}")
    logger.info(f"🔄 允许重置: {config.allow_reset}")
    logger.info(f"🌐 CORS来源: {config.cors_allow_origins}")
    
    # 检查持久化目录
    persist_path = Path(config.persist_directory)
    if persist_path.exists():
        logger.info(f"✅ 持久化目录已存在: {persist_path}")
        
        # 显示现有数据信息
        try:
            db_files = list(persist_path.glob("*.parquet"))
            if db_files:
                logger.info(f"📁 发现 {len(db_files)} 个数据文件")
            else:
                logger.info("📁 持久化目录为空，将创建新的数据库")
        except Exception as e:
            logger.warning(f"检查持久化目录时出错: {e}")
    else:
        logger.info(f"📁 创建持久化目录: {persist_path}")
        persist_path.mkdir(parents=True, exist_ok=True)
    
    logger.info("✅ ChromaDB服务配置完成")
    logger.info("=" * 60)
    
    try:
        # 创建ChromaDB应用
        app = create_chromadb_app()
        
        logger.info("🎯 ChromaDB服务已准备就绪")
        logger.info(f"📖 API文档: http://{config.host}:{config.port}/docs")
        logger.info(f"🔍 健康检查: http://{config.host}:{config.port}/api/v1/heartbeat")
        logger.info("=" * 60)
        
        # 启动服务
        uvicorn.run(
            app,
            host=config.host,
            port=config.port,
            log_level=config.log_level.lower(),
            access_log=True,
        )
        
    except KeyboardInterrupt:
        logger.info("ChromaDB服务已停止")
    except Exception as e:
        logger.error(f"ChromaDB服务启动失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
