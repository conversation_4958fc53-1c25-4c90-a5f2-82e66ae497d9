"""
统一服务配置
合并了原有的分析服务和向量化服务配置
"""

import os
from pathlib import Path
from typing import Optional
from pydantic_settings import BaseSettings


class UnifiedServiceConfig(BaseSettings):
    """统一服务配置"""

    # 服务基本信息
    service_name: str = "unified-service"
    service_version: str = "1.0.0"
    host: str = os.getenv("HOST", "0.0.0.0")
    port: int = int(os.getenv("PORT", "8000"))
    debug: bool = os.getenv("DEBUG", "false").lower() == "true"
    log_level: str = os.getenv("LOG_LEVEL", "INFO")

    # 文件上传配置
    upload_dir: str = os.getenv("UPLOAD_DIR", "./data/uploads")
    max_file_size: int = int(os.getenv("MAX_FILE_SIZE", "100"))  # MB
    allowed_extensions: set = {".csv", ".xlsx", ".xls"}

    # ChromaDB配置 (客户端模式，连接到独立的ChromaDB服务)
    chroma_host: str = os.getenv("CHROMA_HOST", "localhost")
    chroma_port: int = int(os.getenv("CHROMA_PORT", "8001"))
    chroma_collection_prefix: str = os.getenv("CHROMA_COLLECTION_PREFIX", "file_")

    # Redis配置 (用于Celery)
    redis_host: str = os.getenv("REDIS_HOST", "localhost")
    redis_port: int = int(os.getenv("REDIS_PORT", "6379"))
    redis_db: int = int(os.getenv("REDIS_DB", "0"))
    redis_password: Optional[str] = os.getenv("REDIS_PASSWORD", None)

    # Celery配置
    celery_broker_url: str = os.getenv("CELERY_BROKER_URL", "")
    celery_result_backend: str = os.getenv("CELERY_RESULT_BACKEND", "")

    # LLM配置 (保持与原分析服务一致)
    llm_provider: str = os.getenv("LLM_PROVIDER", "deepseek")
    deepseek_api_key: str = os.getenv("DEEPSEEK_API_KEY", "")
    deepseek_model: str = os.getenv("DEEPSEEK_MODEL", "deepseek-reasoner")
    openai_api_key: str = os.getenv("OPENAI_API_KEY", "")
    openai_model: str = os.getenv("OPENAI_MODEL", "gpt-4")
    enable_streaming: bool = os.getenv("ENABLE_STREAMING", "true").lower() == "true"

    # 提示词配置
    prompts_dir: str = os.getenv("PROMPTS_DIR", "./prompts")

    # 任务配置
    task_timeout: int = int(os.getenv("TASK_TIMEOUT", "1800"))  # 30分钟
    task_retry_max: int = int(os.getenv("TASK_RETRY_MAX", "3"))
    task_retry_delay: int = int(os.getenv("TASK_RETRY_DELAY", "60"))  # 60秒

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        
        # 自动构建Redis URL
        if not self.celery_broker_url:
            if self.redis_password:
                self.celery_broker_url = f"redis://:{self.redis_password}@{self.redis_host}:{self.redis_port}/{self.redis_db}"
            else:
                self.celery_broker_url = f"redis://{self.redis_host}:{self.redis_port}/{self.redis_db}"
        
        if not self.celery_result_backend:
            self.celery_result_backend = self.celery_broker_url

    @property
    def chroma_url(self) -> str:
        """ChromaDB服务URL"""
        return f"http://{self.chroma_host}:{self.chroma_port}"

    @property
    def redis_url(self) -> str:
        """Redis连接URL"""
        if self.redis_password:
            return f"redis://:{self.redis_password}@{self.redis_host}:{self.redis_port}/{self.redis_db}"
        else:
            return f"redis://{self.redis_host}:{self.redis_port}/{self.redis_db}"

    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"


# 创建全局配置实例
config = UnifiedServiceConfig()
