"""
任务状态管理器
基于Redis的任务状态跟踪和结果存储
"""

import json
import redis
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict

from shared.utils.logger import get_logger

logger = get_logger(__name__)


@dataclass
class TaskState:
    """任务状态数据类"""
    task_id: str
    task_type: str  # vectorization, analysis, health_check
    status: str  # PENDING, STARTED, PROGRESS, SUCCESS, FAILURE, REVOKED
    file_code: Optional[str] = None
    progress: int = 0
    stage: str = "初始化"
    message: str = ""
    result: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    created_at: Optional[datetime] = None
    started_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    metadata: Optional[Dict[str, Any]] = None

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        data = asdict(self)
        # 处理datetime序列化
        for key, value in data.items():
            if isinstance(value, datetime):
                data[key] = value.isoformat()
        return data

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'TaskState':
        """从字典创建实例"""
        # 处理datetime反序列化
        datetime_fields = ['created_at', 'started_at', 'updated_at', 'completed_at']
        for field in datetime_fields:
            if field in data and data[field]:
                if isinstance(data[field], str):
                    data[field] = datetime.fromisoformat(data[field])
        
        return cls(**data)


class TaskStateManager:
    """任务状态管理器"""
    
    def __init__(self, redis_url: str, key_prefix: str = "task_state"):
        """
        初始化任务状态管理器
        
        Args:
            redis_url: Redis连接URL
            key_prefix: Redis键前缀
        """
        self.redis_client = redis.from_url(redis_url, decode_responses=True)
        self.key_prefix = key_prefix
        self.task_ttl = 86400 * 7  # 任务状态保留7天
        
        logger.info(f"任务状态管理器初始化完成: {redis_url}")
    
    def _get_task_key(self, task_id: str) -> str:
        """获取任务状态的Redis键"""
        return f"{self.key_prefix}:{task_id}"
    
    def _get_file_tasks_key(self, file_code: str) -> str:
        """获取文件相关任务列表的Redis键"""
        return f"{self.key_prefix}:file:{file_code}"
    
    def _get_type_tasks_key(self, task_type: str) -> str:
        """获取任务类型列表的Redis键"""
        return f"{self.key_prefix}:type:{task_type}"
    
    async def create_task_state(
        self,
        task_id: str,
        task_type: str,
        file_code: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> TaskState:
        """
        创建任务状态
        
        Args:
            task_id: 任务ID
            task_type: 任务类型
            file_code: 文件编码
            metadata: 元数据
            
        Returns:
            任务状态对象
        """
        try:
            task_state = TaskState(
                task_id=task_id,
                task_type=task_type,
                status="PENDING",
                file_code=file_code,
                created_at=datetime.now(),
                updated_at=datetime.now(),
                metadata=metadata or {}
            )
            
            # 存储到Redis
            task_key = self._get_task_key(task_id)
            self.redis_client.setex(
                task_key,
                self.task_ttl,
                json.dumps(task_state.to_dict())
            )
            
            # 添加到文件任务列表
            if file_code:
                file_tasks_key = self._get_file_tasks_key(file_code)
                self.redis_client.sadd(file_tasks_key, task_id)
                self.redis_client.expire(file_tasks_key, self.task_ttl)
            
            # 添加到任务类型列表
            type_tasks_key = self._get_type_tasks_key(task_type)
            self.redis_client.sadd(type_tasks_key, task_id)
            self.redis_client.expire(type_tasks_key, self.task_ttl)
            
            logger.debug(f"创建任务状态: {task_id} ({task_type})")
            return task_state
            
        except Exception as e:
            logger.error(f"创建任务状态失败: {e}")
            raise
    
    async def update_task_state(
        self,
        task_id: str,
        status: Optional[str] = None,
        progress: Optional[int] = None,
        stage: Optional[str] = None,
        message: Optional[str] = None,
        result: Optional[Dict[str, Any]] = None,
        error: Optional[str] = None
    ) -> Optional[TaskState]:
        """
        更新任务状态
        
        Args:
            task_id: 任务ID
            status: 状态
            progress: 进度
            stage: 阶段
            message: 消息
            result: 结果
            error: 错误信息
            
        Returns:
            更新后的任务状态
        """
        try:
            # 获取现有状态
            task_state = await self.get_task_state(task_id)
            if not task_state:
                logger.warning(f"任务状态不存在: {task_id}")
                return None
            
            # 更新字段
            if status is not None:
                task_state.status = status
                if status == "STARTED" and not task_state.started_at:
                    task_state.started_at = datetime.now()
                elif status in ["SUCCESS", "FAILURE", "REVOKED"] and not task_state.completed_at:
                    task_state.completed_at = datetime.now()
            
            if progress is not None:
                task_state.progress = progress
            
            if stage is not None:
                task_state.stage = stage
            
            if message is not None:
                task_state.message = message
            
            if result is not None:
                task_state.result = result
            
            if error is not None:
                task_state.error = error
            
            task_state.updated_at = datetime.now()
            
            # 保存到Redis
            task_key = self._get_task_key(task_id)
            self.redis_client.setex(
                task_key,
                self.task_ttl,
                json.dumps(task_state.to_dict())
            )
            
            logger.debug(f"更新任务状态: {task_id} -> {status or task_state.status}")
            return task_state
            
        except Exception as e:
            logger.error(f"更新任务状态失败: {e}")
            raise
    
    async def get_task_state(self, task_id: str) -> Optional[TaskState]:
        """
        获取任务状态
        
        Args:
            task_id: 任务ID
            
        Returns:
            任务状态对象
        """
        try:
            task_key = self._get_task_key(task_id)
            data = self.redis_client.get(task_key)
            
            if not data:
                return None
            
            task_data = json.loads(data)
            return TaskState.from_dict(task_data)
            
        except Exception as e:
            logger.error(f"获取任务状态失败: {e}")
            return None
    
    async def get_file_tasks(self, file_code: str) -> List[TaskState]:
        """
        获取文件相关的所有任务
        
        Args:
            file_code: 文件编码
            
        Returns:
            任务状态列表
        """
        try:
            file_tasks_key = self._get_file_tasks_key(file_code)
            task_ids = self.redis_client.smembers(file_tasks_key)
            
            tasks = []
            for task_id in task_ids:
                task_state = await self.get_task_state(task_id)
                if task_state:
                    tasks.append(task_state)
            
            # 按创建时间排序
            tasks.sort(key=lambda x: x.created_at or datetime.min)
            return tasks
            
        except Exception as e:
            logger.error(f"获取文件任务失败: {e}")
            return []
    
    async def get_tasks_by_type(self, task_type: str, limit: int = 100) -> List[TaskState]:
        """
        按类型获取任务
        
        Args:
            task_type: 任务类型
            limit: 限制数量
            
        Returns:
            任务状态列表
        """
        try:
            type_tasks_key = self._get_type_tasks_key(task_type)
            task_ids = list(self.redis_client.smembers(type_tasks_key))[:limit]
            
            tasks = []
            for task_id in task_ids:
                task_state = await self.get_task_state(task_id)
                if task_state:
                    tasks.append(task_state)
            
            # 按更新时间排序
            tasks.sort(key=lambda x: x.updated_at or datetime.min, reverse=True)
            return tasks
            
        except Exception as e:
            logger.error(f"获取类型任务失败: {e}")
            return []
    
    async def get_active_tasks(self) -> List[TaskState]:
        """
        获取活跃任务（PENDING, STARTED, PROGRESS状态）
        
        Returns:
            活跃任务列表
        """
        try:
            active_statuses = ["PENDING", "STARTED", "PROGRESS"]
            active_tasks = []
            
            # 遍历所有任务类型
            for task_type in ["vectorization", "analysis", "health_check"]:
                tasks = await self.get_tasks_by_type(task_type)
                for task in tasks:
                    if task.status in active_statuses:
                        active_tasks.append(task)
            
            return active_tasks
            
        except Exception as e:
            logger.error(f"获取活跃任务失败: {e}")
            return []
    
    async def cleanup_expired_tasks(self, days: int = 7):
        """
        清理过期任务
        
        Args:
            days: 保留天数
        """
        try:
            cutoff_time = datetime.now() - timedelta(days=days)
            cleaned_count = 0
            
            # 获取所有任务类型的任务
            for task_type in ["vectorization", "analysis", "health_check"]:
                tasks = await self.get_tasks_by_type(task_type, limit=1000)
                
                for task in tasks:
                    if task.updated_at and task.updated_at < cutoff_time:
                        # 删除任务状态
                        task_key = self._get_task_key(task.task_id)
                        self.redis_client.delete(task_key)
                        
                        # 从文件任务列表中移除
                        if task.file_code:
                            file_tasks_key = self._get_file_tasks_key(task.file_code)
                            self.redis_client.srem(file_tasks_key, task.task_id)
                        
                        # 从类型任务列表中移除
                        type_tasks_key = self._get_type_tasks_key(task.task_type)
                        self.redis_client.srem(type_tasks_key, task.task_id)
                        
                        cleaned_count += 1
            
            if cleaned_count > 0:
                logger.info(f"清理了 {cleaned_count} 个过期任务")
            
        except Exception as e:
            logger.error(f"清理过期任务失败: {e}")
    
    async def get_statistics(self) -> Dict[str, Any]:
        """
        获取任务统计信息
        
        Returns:
            统计信息字典
        """
        try:
            stats = {
                "total_tasks": 0,
                "by_type": {},
                "by_status": {},
                "active_tasks": 0,
            }
            
            # 按类型统计
            for task_type in ["vectorization", "analysis", "health_check"]:
                tasks = await self.get_tasks_by_type(task_type, limit=1000)
                stats["by_type"][task_type] = len(tasks)
                stats["total_tasks"] += len(tasks)
                
                # 按状态统计
                for task in tasks:
                    status = task.status
                    if status not in stats["by_status"]:
                        stats["by_status"][status] = 0
                    stats["by_status"][status] += 1
                    
                    if status in ["PENDING", "STARTED", "PROGRESS"]:
                        stats["active_tasks"] += 1
            
            return stats
            
        except Exception as e:
            logger.error(f"获取任务统计失败: {e}")
            return {}
    
    def close(self):
        """关闭Redis连接"""
        if self.redis_client:
            self.redis_client.close()
