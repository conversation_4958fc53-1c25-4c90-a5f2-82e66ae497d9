"""
ChromaDB服务配置
独立运行的ChromaDB服务端配置
"""

import os
from pathlib import Path
from pydantic_settings import BaseSettings


class ChromaDBServiceConfig(BaseSettings):
    """ChromaDB服务配置"""

    # 服务基本信息
    service_name: str = "chromadb-service"
    service_version: str = "1.0.0"
    host: str = os.getenv("CHROMA_HOST", "0.0.0.0")
    port: int = int(os.getenv("CHROMA_PORT", "8001"))
    log_level: str = os.getenv("LOG_LEVEL", "INFO")

    # 数据存储配置
    persist_directory: str = os.getenv("CHROMA_PERSIST_DIR", "./data/chromadb")
    
    # 认证配置（可选）
    auth_enabled: bool = os.getenv("CHROMA_AUTH_ENABLED", "false").lower() == "true"
    auth_token: str = os.getenv("CHROMA_AUTH_TOKEN", "")
    
    # 性能配置
    max_batch_size: int = int(os.getenv("CHROMA_MAX_BATCH_SIZE", "1000"))
    allow_reset: bool = os.getenv("CHROMA_ALLOW_RESET", "true").lower() == "true"
    
    # CORS配置
    cors_allow_origins: list = ["*"]  # 生产环境应该限制具体域名
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        
        # 确保持久化目录存在
        Path(self.persist_directory).mkdir(parents=True, exist_ok=True)

    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"


# 创建全局配置实例
config = ChromaDBServiceConfig()
