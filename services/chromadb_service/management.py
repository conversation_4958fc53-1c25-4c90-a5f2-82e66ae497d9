"""
ChromaDB服务管理工具
提供数据库管理、备份、恢复等功能
"""

import json
import shutil
import httpx
from pathlib import Path
from datetime import datetime
from typing import Dict, Any, List, Optional

from shared.utils.logger import get_logger
from services.chromadb_service.config import config

logger = get_logger(__name__)


class ChromaDBManager:
    """ChromaDB管理器"""
    
    def __init__(self, base_url: str = None):
        """
        初始化ChromaDB管理器
        
        Args:
            base_url: ChromaDB服务URL
        """
        self.base_url = base_url or f"http://{config.host}:{config.port}"
        self.client = httpx.Client(timeout=30.0)
        
        logger.info(f"ChromaDB管理器初始化: {self.base_url}")
    
    def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        try:
            response = self.client.get(f"{self.base_url}/api/v1/heartbeat")
            
            if response.status_code == 200:
                return {
                    "status": "healthy",
                    "response_time": response.elapsed.total_seconds(),
                    "version": response.json().get("nanosecond heartbeat", "unknown")
                }
            else:
                return {
                    "status": "unhealthy",
                    "error": f"HTTP {response.status_code}"
                }
                
        except Exception as e:
            return {
                "status": "error",
                "error": str(e)
            }
    
    def list_collections(self) -> List[Dict[str, Any]]:
        """列出所有集合"""
        try:
            response = self.client.get(f"{self.base_url}/api/v1/collections")
            
            if response.status_code == 200:
                collections = response.json()
                logger.info(f"发现 {len(collections)} 个集合")
                return collections
            else:
                logger.error(f"获取集合列表失败: HTTP {response.status_code}")
                return []
                
        except Exception as e:
            logger.error(f"获取集合列表失败: {e}")
            return []
    
    def get_collection_info(self, collection_name: str) -> Optional[Dict[str, Any]]:
        """获取集合信息"""
        try:
            response = self.client.get(f"{self.base_url}/api/v1/collections/{collection_name}")
            
            if response.status_code == 200:
                collection_info = response.json()
                
                # 获取文档数量
                count_response = self.client.get(
                    f"{self.base_url}/api/v1/collections/{collection_name}/count"
                )
                
                if count_response.status_code == 200:
                    count_data = count_response.json()
                    collection_info["document_count"] = count_data.get("count", 0)
                
                return collection_info
            else:
                logger.error(f"获取集合信息失败: HTTP {response.status_code}")
                return None
                
        except Exception as e:
            logger.error(f"获取集合信息失败: {e}")
            return None
    
    def delete_collection(self, collection_name: str) -> bool:
        """删除集合"""
        try:
            response = self.client.delete(f"{self.base_url}/api/v1/collections/{collection_name}")
            
            if response.status_code == 200:
                logger.info(f"集合删除成功: {collection_name}")
                return True
            else:
                logger.error(f"集合删除失败: HTTP {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"集合删除失败: {e}")
            return False
    
    def backup_database(self, backup_dir: str) -> bool:
        """备份数据库"""
        try:
            backup_path = Path(backup_dir)
            backup_path.mkdir(parents=True, exist_ok=True)
            
            # 创建时间戳目录
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_target = backup_path / f"chromadb_backup_{timestamp}"
            
            # 复制持久化目录
            persist_path = Path(config.persist_directory)
            if persist_path.exists():
                shutil.copytree(persist_path, backup_target)
                
                # 创建备份信息文件
                backup_info = {
                    "backup_time": datetime.now().isoformat(),
                    "source_directory": str(persist_path),
                    "backup_directory": str(backup_target),
                    "collections": self.list_collections()
                }
                
                info_file = backup_target / "backup_info.json"
                with open(info_file, 'w', encoding='utf-8') as f:
                    json.dump(backup_info, f, indent=2, ensure_ascii=False)
                
                logger.info(f"数据库备份成功: {backup_target}")
                return True
            else:
                logger.error(f"持久化目录不存在: {persist_path}")
                return False
                
        except Exception as e:
            logger.error(f"数据库备份失败: {e}")
            return False
    
    def restore_database(self, backup_dir: str) -> bool:
        """恢复数据库"""
        try:
            backup_path = Path(backup_dir)
            if not backup_path.exists():
                logger.error(f"备份目录不存在: {backup_path}")
                return False
            
            # 检查备份信息
            info_file = backup_path / "backup_info.json"
            if info_file.exists():
                with open(info_file, 'r', encoding='utf-8') as f:
                    backup_info = json.load(f)
                logger.info(f"备份信息: {backup_info['backup_time']}")
            
            # 停止服务前的警告
            logger.warning("恢复数据库需要停止ChromaDB服务")
            
            # 备份当前数据
            current_persist = Path(config.persist_directory)
            if current_persist.exists():
                backup_current = current_persist.parent / f"chromadb_current_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                shutil.copytree(current_persist, backup_current)
                logger.info(f"当前数据已备份到: {backup_current}")
            
            # 恢复数据
            if current_persist.exists():
                shutil.rmtree(current_persist)
            
            shutil.copytree(backup_path, current_persist)
            
            logger.info(f"数据库恢复成功: {backup_path} -> {current_persist}")
            return True
            
        except Exception as e:
            logger.error(f"数据库恢复失败: {e}")
            return False
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取数据库统计信息"""
        try:
            collections = self.list_collections()
            
            stats = {
                "total_collections": len(collections),
                "total_documents": 0,
                "collections_detail": [],
                "disk_usage": self._get_disk_usage(),
                "service_status": self.health_check()
            }
            
            for collection in collections:
                collection_name = collection.get("name", "")
                collection_info = self.get_collection_info(collection_name)
                
                if collection_info:
                    doc_count = collection_info.get("document_count", 0)
                    stats["total_documents"] += doc_count
                    
                    stats["collections_detail"].append({
                        "name": collection_name,
                        "document_count": doc_count,
                        "metadata": collection_info.get("metadata", {})
                    })
            
            return stats
            
        except Exception as e:
            logger.error(f"获取统计信息失败: {e}")
            return {}
    
    def _get_disk_usage(self) -> Dict[str, Any]:
        """获取磁盘使用情况"""
        try:
            persist_path = Path(config.persist_directory)
            
            if not persist_path.exists():
                return {"error": "持久化目录不存在"}
            
            total_size = 0
            file_count = 0
            
            for file_path in persist_path.rglob("*"):
                if file_path.is_file():
                    total_size += file_path.stat().st_size
                    file_count += 1
            
            return {
                "total_size_bytes": total_size,
                "total_size_mb": round(total_size / (1024 * 1024), 2),
                "file_count": file_count,
                "directory": str(persist_path)
            }
            
        except Exception as e:
            logger.error(f"获取磁盘使用情况失败: {e}")
            return {"error": str(e)}
    
    def cleanup_empty_collections(self) -> int:
        """清理空集合"""
        try:
            collections = self.list_collections()
            cleaned_count = 0
            
            for collection in collections:
                collection_name = collection.get("name", "")
                collection_info = self.get_collection_info(collection_name)
                
                if collection_info and collection_info.get("document_count", 0) == 0:
                    if self.delete_collection(collection_name):
                        cleaned_count += 1
                        logger.info(f"清理空集合: {collection_name}")
            
            logger.info(f"清理了 {cleaned_count} 个空集合")
            return cleaned_count
            
        except Exception as e:
            logger.error(f"清理空集合失败: {e}")
            return 0
    
    def close(self):
        """关闭客户端"""
        self.client.close()
