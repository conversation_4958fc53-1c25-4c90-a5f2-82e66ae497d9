"""
LLM客户端
基于src中的LLM工厂模式，用于Worker中的非流式分析
"""

import sys
from pathlib import Path

# 添加src路径到Python路径
src_path = Path(__file__).parent.parent.parent.parent / "src"
sys.path.insert(0, str(src_path))

from src.llm.factory import create_llm_from_settings
from shared.utils.logger import get_logger
from services.worker_service.config import config

logger = get_logger(__name__)


class LLMClient:
    """LLM客户端 - 基于工厂模式"""

    def __init__(self):
        self.llm = None
        self._initialize_llm()

        logger.info(f"LLM客户端初始化完成: 提供商={config.llm_provider}")

    def _initialize_llm(self):
        """初始化LLM实例"""
        try:
            # 使用工厂模式创建LLM实例
            self.llm = create_llm_from_settings(
                settings=config,
                instance_key="worker_llm",
                langchain_compatible=False  # Worker中不需要LangChain兼容性
            )

            logger.info(f"LLM实例创建成功: {self.llm.get_provider().value} - {self.llm.get_model()}")

        except Exception as e:
            logger.error(f"LLM实例创建失败: {e}")
            raise

    async def analyze(
        self,
        prompt: str,
        analysis_type: str = "default",
        streaming: bool = False
    ) -> str:
        """
        执行LLM分析

        Args:
            prompt: 分析提示词
            analysis_type: 分析类型
            streaming: 是否流式输出 (Worker中固定为False)

        Returns:
            分析结果文本
        """
        try:
            if not self.llm:
                raise ValueError("LLM实例未初始化")

            # 根据分析类型调整参数
            kwargs = self._get_analysis_params(analysis_type)

            logger.debug(f"开始LLM分析: 类型={analysis_type}, 提供商={self.llm.get_provider().value}")

            # 调用LLM
            response = self.llm.invoke(prompt, **kwargs)

            logger.debug(f"LLM分析完成，响应长度: {len(response.content)}")
            return response.content

        except Exception as e:
            logger.error(f"LLM分析失败: {e}")
            raise

    def _get_analysis_params(self, analysis_type: str) -> dict:
        """根据分析类型获取参数"""
        params = {}

        if analysis_type == "quick":
            params.update({
                "max_tokens": 1000,
                "temperature": 0.1
            })
        elif analysis_type == "detailed":
            params.update({
                "max_tokens": 4000,
                "temperature": 0.2
            })
        else:  # default
            params.update({
                "max_tokens": 2000,
                "temperature": 0.1
            })

        return params

    async def health_check(self) -> bool:
        """健康检查"""
        try:
            if not self.llm:
                return False

            # 使用工厂模式的LLM进行简单测试
            test_response = self.llm.invoke("test", max_tokens=1)
            return test_response is not None

        except Exception as e:
            logger.error(f"LLM健康检查失败: {e}")
            return False

    async def cleanup(self):
        """清理资源"""
        # LLM工厂模式的实例会自动管理资源
        pass
