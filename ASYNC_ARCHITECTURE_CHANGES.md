# Deep Risk RAG 异步架构改造文档

## 概述

本次改造将原有的同步架构升级为基于 Celery + Redis 的异步架构，实现了文件上传、向量化处理和风险分析的完全异步化，提升了系统的并发处理能力和用户体验。

## 架构变化

### 原架构
```
Client -> Analysis Service -> Vector Service -> ChromaDB (本地)
                           -> LLM API
```

### 新架构
```
Client -> Unified Web Service -> Redis (Message Queue) -> Celery Workers
                              -> ChromaDB (Server Mode)
                              -> Task State Manager
```

## 主要改动

### 1. 环境依赖更新

#### 新增依赖包
- `celery[redis]>=5.3.0` - 异步任务队列
- `redis>=4.5.0` - 消息队列和缓存
- `flower>=2.0.0` - Celery任务监控
- `httpx>=0.24.0` - 异步HTTP客户端
- `psutil>=5.9.0` - 系统监控

#### 文件变更
- `requirements.txt` - 添加新依赖包

### 2. 共享组件

#### 新增文件
- `shared/celery_config.py` - Celery配置和任务定义
- `shared/task_state_manager.py` - 基于Redis的任务状态管理

#### 配置内容
- Celery应用配置
- 任务路由和队列配置
- 任务状态跟踪机制
- 重试策略配置

### 3. 统一Web服务 (services/unified_service/)

#### 核心特性
- 合并了原有分析服务和向量化服务的API
- 保持与原有API的完全兼容性
- 异步任务提交和状态查询
- 支持流式和非流式两种分析模式

#### 主要文件
```
services/unified_service/
├── main.py                 # FastAPI应用入口
├── config.py              # 统一配置管理
├── models/                # 数据模型
│   ├── requests.py        # 请求模型
│   └── responses.py       # 响应模型
├── api/                   # API路由
│   ├── health.py          # 健康检查
│   ├── upload.py          # 文件上传
│   ├── analyze.py         # 风险分析
│   ├── status.py          # 状态查询
│   ├── files.py           # 文件管理
│   ├── tasks.py           # 任务管理
│   └── results.py         # 结果查询
└── core/                  # 核心组件
    ├── health_checker.py  # 健康检查器
    ├── file_manager.py    # 文件管理器
    ├── task_manager.py    # 任务管理器
    └── chroma_client.py   # ChromaDB客户端
```

#### API端点
- `POST /upload/` - 文件上传和异步向量化
- `POST /analyze/{file_code}` - 风险分析（支持流式/非流式）
- `GET /status/{file_code}` - 文件处理状态
- `GET /tasks/{task_id}` - 任务状态查询
- `GET /result/{analysis_id}` - 分析结果获取
- `GET /files/` - 文件列表管理
- `GET /health` - 系统健康检查

### 4. Celery Worker服务 (services/worker_service/)

#### 核心特性
- 专门处理BGE-M3向量化任务
- 基于src中LLM工厂模式的风险分析
- GPU资源优化和内存管理
- 任务进度实时更新

#### 主要文件
```
services/worker_service/
├── worker.py              # Worker启动入口
├── config.py              # Worker配置
├── tasks/                 # Celery任务定义
│   ├── vectorization.py  # 向量化任务
│   ├── analysis.py       # 分析任务
│   └── health.py         # 健康检查任务
└── core/                 # 核心组件
    ├── vectorizer.py     # 向量化处理器
    ├── analyzer.py       # 风险分析器
    ├── embeddings.py     # BGE嵌入服务
    └── llm_client.py     # LLM客户端（基于工厂模式）
```

#### 任务类型
- `vectorize_file` - 文件向量化任务
- `analyze_risk` - 风险分析任务
- `health_check` - 健康检查任务

### 5. ChromaDB服务 (services/chromadb_service/)

#### 核心特性
- 独立的ChromaDB服务端
- HTTP API接口
- 数据持久化管理
- 备份和恢复功能

#### 主要文件
```
services/chromadb_service/
├── server.py              # ChromaDB服务器
├── config.py              # 服务配置
└── management.py          # 管理工具
```

#### 管理功能
- 集合管理和统计
- 数据备份和恢复
- 健康检查和监控
- 磁盘使用统计

### 6. Docker配置更新

#### 新增服务
- `redis` - 消息队列服务
- `unified-service` - 统一Web服务
- `worker-service` - Celery Worker服务
- `flower` - 任务监控服务

#### 文件变更
- `docker/docker-compose.yml` - 生产环境配置
- `docker/docker-compose.dev.yml` - 开发环境配置
- `docker/unified-service.Dockerfile` - 统一服务镜像
- `docker/worker-service.Dockerfile` - Worker服务镜像

#### 特性
- GPU支持配置
- 健康检查机制
- 数据卷持久化
- 网络隔离配置

### 7. 启动脚本和工具

#### 新增脚本
```
scripts/
├── start_services.sh      # 服务启动脚本
├── quick_start.sh         # 快速启动脚本
├── monitor.sh             # 系统监控脚本
├── docker_start.sh        # Docker启动脚本
├── start_chromadb.sh      # ChromaDB启动脚本
└── chromadb_cli.py        # ChromaDB管理工具
```

#### 功能特性
- 一键启动所有服务
- 实时监控和日志查看
- 服务状态检查
- 资源使用监控
- ChromaDB数据管理

### 8. 配置文件更新

#### 环境配置
- `.env.example` - 完整的环境变量模板
- 支持开发和生产环境配置
- LLM提供商配置
- 任务队列配置
- 监控配置

#### 主要配置项
```bash
# LLM配置
LLM_PROVIDER=deepseek
DEEPSEEK_API_KEY=your_api_key
ENABLE_STREAMING=true

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379

# ChromaDB配置
CHROMA_HOST=localhost
CHROMA_PORT=8001

# BGE-M3模型配置
MODEL_NAME=./models/bge-m3-safetensors-only
DEVICE=auto
BATCH_SIZE=32

# 任务配置
TASK_TIMEOUT=1800
TASK_RETRY_MAX=3
```

## 兼容性保证

### API兼容性
- 保持所有原有API端点不变
- 响应格式完全兼容
- 错误处理机制一致

### 功能兼容性
- 文件上传和处理逻辑不变
- BGE-M3向量化算法不变
- LLM调用逻辑基于原有工厂模式
- 分析结果格式不变

### 数据兼容性
- ChromaDB数据格式不变
- 文件存储结构不变
- 配置文件向后兼容

## 性能提升

### 并发处理
- 支持多文件同时上传
- 异步向量化处理
- 并行风险分析
- 任务队列优化

### 资源优化
- GPU内存智能管理
- 模型自动卸载
- 批处理优化
- 连接池复用

### 用户体验
- 实时任务进度
- 非阻塞操作
- 流式分析输出
- 详细状态反馈

## 监控和运维

### 任务监控
- Flower Web界面
- 实时任务状态
- 队列长度监控
- 失败任务重试

### 系统监控
- 服务健康检查
- 资源使用监控
- 日志聚合查看
- 性能指标统计

### 运维工具
- 一键启动/停止
- 服务状态检查
- 数据备份恢复
- 配置管理

## 部署方式

### 本地开发
```bash
# 快速启动
./scripts/quick_start.sh

# 分别启动服务
./scripts/start_services.sh -d all
```

### Docker部署
```bash
# 开发环境
./scripts/docker_start.sh -e dev

# 生产环境
./scripts/docker_start.sh -e prod -d
```

### 服务监控
```bash
# 查看状态
./scripts/monitor.sh -s

# 持续监控
./scripts/monitor.sh -w

# 查看日志
./scripts/monitor.sh -l
```

## 迁移指南

### 从原架构迁移
1. 停止原有服务
2. 安装新依赖：`pip install -r requirements.txt`
3. 配置环境变量：复制`.env.example`到`.env`
4. 启动新服务：`./scripts/quick_start.sh`
5. 验证功能：访问`http://localhost:8000/docs`

### 数据迁移
- ChromaDB数据自动兼容
- 无需额外迁移步骤
- 建议备份现有数据

### 配置迁移
- 原有配置文件继续有效
- 新增配置项使用默认值
- 建议更新为新的配置格式

## 总结

本次异步架构改造实现了：

1. **完全异步化** - 所有耗时操作都异步处理
2. **高并发支持** - 支持多用户同时使用
3. **实时监控** - 完整的任务状态跟踪
4. **向后兼容** - 保持原有API完全兼容
5. **易于部署** - 提供多种部署方式
6. **运维友好** - 完善的监控和管理工具

系统现在具备了生产环境的可扩展性和稳定性，同时保持了开发环境的易用性。
