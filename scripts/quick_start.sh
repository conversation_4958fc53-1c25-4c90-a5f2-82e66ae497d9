#!/bin/bash

# Deep Risk RAG 快速启动脚本
# 一键启动所有必需服务

set -e

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_message() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_header() {
    echo -e "${BLUE}$1${NC}"
}

# 显示欢迎信息
show_welcome() {
    print_header "🎯 Deep Risk RAG 异步架构快速启动"
    echo ""
    echo "这个脚本将帮助您快速启动Deep Risk RAG系统的所有服务："
    echo "  🔴 Redis (消息队列)"
    echo "  🗄️  ChromaDB (向量数据库)"
    echo "  🌐 统一Web服务 (API)"
    echo "  ⚙️  Celery Worker (向量化和分析)"
    echo "  🌸 Flower (任务监控)"
    echo ""
}

# 检查环境
check_environment() {
    print_header "🔍 检查运行环境..."
    
    # 检查Python
    if ! command -v python &> /dev/null; then
        echo "❌ Python未安装，请先安装Python 3.11+"
        exit 1
    fi
    
    python_version=$(python --version 2>&1 | cut -d' ' -f2)
    print_message "Python版本: $python_version"
    
    # 检查pip
    if ! command -v pip &> /dev/null; then
        echo "❌ pip未安装，请先安装pip"
        exit 1
    fi
    
    # 检查.env文件
    if [ ! -f ".env" ]; then
        print_warning ".env文件不存在"
        echo "正在从.env.example创建.env文件..."
        cp .env.example .env
        echo ""
        echo "⚠️  请编辑.env文件并设置您的API密钥："
        echo "   DEEPSEEK_API_KEY=your_api_key_here"
        echo ""
        read -p "按Enter键继续，或Ctrl+C退出编辑.env文件..."
    fi
    
    # 检查模型文件
    if [ ! -d "models/bge-m3-safetensors-only" ]; then
        print_warning "BGE-M3模型文件不存在"
        echo "请确保模型文件位于: models/bge-m3-safetensors-only/"
        echo "您可以从Hugging Face下载或使用其他路径"
        echo ""
        read -p "按Enter键继续..."
    fi
    
    print_message "环境检查完成"
}

# 安装依赖
install_dependencies() {
    print_header "📦 安装依赖..."
    
    # 检查虚拟环境
    if [ -z "$VIRTUAL_ENV" ]; then
        print_warning "建议使用Python虚拟环境"
        echo "创建虚拟环境: python -m venv venv"
        echo "激活虚拟环境: source venv/bin/activate"
        echo ""
        read -p "是否继续安装到全局环境? (y/N): " confirm
        if [ "$confirm" != "y" ] && [ "$confirm" != "Y" ]; then
            echo "已取消安装"
            exit 0
        fi
    else
        print_message "使用虚拟环境: $VIRTUAL_ENV"
    fi
    
    # 安装依赖
    print_message "安装Python依赖..."
    pip install -r requirements.txt
    
    print_message "依赖安装完成"
}

# 创建必要目录
create_directories() {
    print_header "📁 创建必要目录..."
    
    mkdir -p data/uploads
    mkdir -p data/chromadb
    mkdir -p logs
    mkdir -p pids
    
    print_message "目录创建完成"
}

# 启动服务
start_services() {
    print_header "🚀 启动服务..."
    
    # 加载环境变量
    if [ -f ".env" ]; then
        export $(cat .env | grep -v '^#' | xargs)
    fi
    
    # 启动Redis (如果未运行)
    print_message "检查Redis服务..."
    if ! redis-cli ping &> /dev/null; then
        print_message "启动Redis服务..."
        if command -v redis-server &> /dev/null; then
            redis-server --daemonize yes --port ${REDIS_PORT:-6379}
        else
            print_warning "Redis未安装，尝试使用Docker..."
            docker run -d --name deep-risk-redis -p ${REDIS_PORT:-6379}:6379 redis:7-alpine
        fi
        sleep 2
    else
        print_message "Redis服务已运行"
    fi
    
    # 启动ChromaDB
    print_message "启动ChromaDB服务..."
    nohup python services/chromadb_service/server.py > logs/chromadb.log 2>&1 &
    echo $! > pids/chromadb.pid
    sleep 3
    
    # 启动统一Web服务
    print_message "启动统一Web服务..."
    nohup python -m uvicorn services.unified_service.main:app \
        --host ${HOST:-0.0.0.0} \
        --port ${PORT:-8000} > logs/unified.log 2>&1 &
    echo $! > pids/unified.pid
    sleep 2
    
    # 启动Celery Worker
    print_message "启动Celery Worker服务..."
    nohup celery -A services.worker_service.worker worker \
        --loglevel=info \
        --concurrency=1 \
        --prefetch-multiplier=1 \
        --max-tasks-per-child=10 > logs/worker.log 2>&1 &
    echo $! > pids/worker.pid
    sleep 2
    
    # 启动Flower监控
    print_message "启动Flower监控..."
    nohup celery -A services.worker_service.worker flower \
        --port=${FLOWER_PORT:-5555} > logs/flower.log 2>&1 &
    echo $! > pids/flower.pid
    sleep 2
    
    print_message "所有服务启动完成"
}

# 检查服务状态
check_services() {
    print_header "📊 检查服务状态..."
    
    local all_healthy=true
    
    # 检查Redis
    if redis-cli ping &> /dev/null; then
        echo "✅ Redis: 运行中"
    else
        echo "❌ Redis: 未运行"
        all_healthy=false
    fi
    
    # 检查ChromaDB
    if curl -f http://localhost:${CHROMA_PORT:-8001}/api/v1/heartbeat &> /dev/null; then
        echo "✅ ChromaDB: 运行中"
    else
        echo "❌ ChromaDB: 未运行"
        all_healthy=false
    fi
    
    # 检查统一Web服务
    if curl -f http://localhost:${PORT:-8000}/health &> /dev/null; then
        echo "✅ 统一Web服务: 运行中"
    else
        echo "❌ 统一Web服务: 未运行"
        all_healthy=false
    fi
    
    # 检查Flower
    if curl -f http://localhost:${FLOWER_PORT:-5555} &> /dev/null; then
        echo "✅ Flower监控: 运行中"
    else
        echo "❌ Flower监控: 未运行"
        all_healthy=false
    fi
    
    echo ""
    
    if [ "$all_healthy" = true ]; then
        print_message "🎉 所有服务运行正常！"
    else
        print_warning "⚠️  部分服务可能未正常启动，请检查日志"
    fi
}

# 显示访问信息
show_access_info() {
    print_header "🌐 服务访问信息:"
    echo ""
    echo "📱 主要服务:"
    echo "  统一Web服务:  http://localhost:${PORT:-8000}"
    echo "  API文档:      http://localhost:${PORT:-8000}/docs"
    echo "  健康检查:     http://localhost:${PORT:-8000}/health"
    echo ""
    echo "🔧 管理工具:"
    echo "  ChromaDB:     http://localhost:${CHROMA_PORT:-8001}"
    echo "  Flower监控:   http://localhost:${FLOWER_PORT:-5555}"
    echo ""
    echo "📋 常用命令:"
    echo "  查看日志:     tail -f logs/*.log"
    echo "  停止服务:     ./scripts/start_services.sh -s all"
    echo "  重启服务:     ./scripts/start_services.sh -r all"
    echo ""
    echo "📖 使用说明:"
    echo "  1. 访问 http://localhost:${PORT:-8000}/docs 查看API文档"
    echo "  2. 上传CSV/Excel文件进行向量化"
    echo "  3. 使用分析API进行风险评估"
    echo "  4. 在Flower中监控任务状态"
    echo ""
}

# 主函数
main() {
    show_welcome
    
    # 检查是否需要帮助
    if [ "$1" = "-h" ] || [ "$1" = "--help" ]; then
        echo "用法: $0 [选项]"
        echo ""
        echo "选项:"
        echo "  -h, --help    显示帮助信息"
        echo "  -s, --skip    跳过依赖安装"
        echo ""
        exit 0
    fi
    
    check_environment
    
    # 如果不是跳过模式，安装依赖
    if [ "$1" != "-s" ] && [ "$1" != "--skip" ]; then
        install_dependencies
    fi
    
    create_directories
    start_services
    
    # 等待服务启动
    print_message "等待服务完全启动..."
    sleep 5
    
    check_services
    show_access_info
    
    print_header "🎯 Deep Risk RAG 系统已启动完成！"
    echo ""
    echo "💡 提示: 使用 Ctrl+C 不会停止后台服务"
    echo "   要停止所有服务，请运行: ./scripts/start_services.sh -s all"
    echo ""
}

# 执行主函数
main "$@"
