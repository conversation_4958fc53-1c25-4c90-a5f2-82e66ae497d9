# 开发环境Docker Compose配置
# 包含开发调试功能和热重载

version: '3.8'

services:
  # Redis (消息队列和缓存)
  redis:
    image: redis:7-alpine
    container_name: deep-risk-redis-dev
    ports:
      - "6379:6379"
    volumes:
      - redis_dev_data:/data
    command: redis-server --appendonly yes
    networks:
      - deep-risk-dev-network
    restart: unless-stopped

  # ChromaDB 独立服务
  chromadb:
    image: chromadb/chroma:latest
    container_name: deep-risk-chromadb-dev
    ports:
      - "8001:8000"
    volumes:
      - chromadb_dev_data:/chroma/chroma
    environment:
      - CHROMA_SERVER_HOST=0.0.0.0
      - CHROMA_SERVER_HTTP_PORT=8000
      - ANONYMIZED_TELEMETRY=False
      - ALLOW_RESET=true
    networks:
      - deep-risk-dev-network
    restart: unless-stopped

  # 统一Web服务 (开发模式)
  unified-service:
    build:
      context: ..
      dockerfile: docker/unified-service.Dockerfile
    container_name: deep-risk-unified-service-dev
    ports:
      - "8000:8000"
    volumes:
      - ../:/app  # 挂载整个项目目录用于热重载
      - ../data:/app/data
      - ../prompts:/app/prompts
    environment:
      - SERVICE_NAME=unified-service
      - HOST=0.0.0.0
      - PORT=8000
      - DEBUG=true
      - LOG_LEVEL=DEBUG
      # ChromaDB配置
      - CHROMA_HOST=chromadb
      - CHROMA_PORT=8000
      # Redis配置
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_DB=0
      # LLM配置
      - LLM_PROVIDER=deepseek
      - DEEPSEEK_API_KEY=${DEEPSEEK_API_KEY}
      - DEEPSEEK_MODEL=${DEEPSEEK_MODEL:-deepseek-reasoner}
      - ENABLE_STREAMING=true
      # 文件上传配置
      - UPLOAD_DIR=/app/data/uploads
      - MAX_FILE_SIZE=100
      # 任务配置
      - TASK_TIMEOUT=1800
      - TASK_RETRY_MAX=3
    depends_on:
      - redis
      - chromadb
    networks:
      - deep-risk-dev-network
    restart: unless-stopped
    # 开发模式启动命令 (支持热重载)
    command: ["python", "-m", "uvicorn", "services.unified_service.main:app", "--host", "0.0.0.0", "--port", "8000", "--reload"]

  # Celery Worker服务 (开发模式)
  worker-service:
    build:
      context: ..
      dockerfile: docker/worker-service.Dockerfile
    container_name: deep-risk-worker-service-dev
    volumes:
      - ../:/app  # 挂载整个项目目录用于热重载
      - ../models:/app/models
      - ../data:/app/data
      - ../prompts:/app/prompts
    environment:
      - SERVICE_NAME=worker-service
      - LOG_LEVEL=DEBUG
      # BGE-M3模型配置
      - MODEL_NAME=/app/models/bge-m3-safetensors-only
      - DEVICE=cpu  # 开发环境使用CPU
      - BATCH_SIZE=16  # 较小的批次大小
      - MAX_LENGTH=8192
      # ChromaDB配置
      - CHROMA_HOST=chromadb
      - CHROMA_PORT=8000
      # Redis配置
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_DB=0
      # LLM配置
      - LLM_PROVIDER=deepseek
      - DEEPSEEK_API_KEY=${DEEPSEEK_API_KEY}
      - DEEPSEEK_MODEL=${DEEPSEEK_MODEL:-deepseek-reasoner}
      # 任务配置
      - TASK_TIMEOUT=1800
      - MAX_RETRIES=3
      - CHUNK_SIZE=500  # 较小的分块大小
      - CHUNK_OVERLAP=100
      # 内存管理
      - ENABLE_MEMORY_MONITORING=true
      - AUTO_CLEANUP=true
      - MEMORY_CLEANUP_THRESHOLD=70.0
    depends_on:
      - redis
      - chromadb
    networks:
      - deep-risk-dev-network
    restart: unless-stopped
    # 开发模式启动命令
    command: ["celery", "-A", "services.worker_service.worker", "worker", "--loglevel=debug", "--concurrency=1"]

  # Flower监控 (Celery任务监控)
  flower:
    image: mher/flower:0.9.7
    container_name: deep-risk-flower-dev
    ports:
      - "5555:5555"
    environment:
      - CELERY_BROKER_URL=redis://redis:6379/0
      - FLOWER_PORT=5555
    depends_on:
      - redis
    networks:
      - deep-risk-dev-network
    restart: unless-stopped

  # Redis Commander (Redis管理界面)
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: deep-risk-redis-commander-dev
    ports:
      - "8081:8081"
    environment:
      - REDIS_HOSTS=local:redis:6379
    depends_on:
      - redis
    networks:
      - deep-risk-dev-network
    restart: unless-stopped

networks:
  deep-risk-dev-network:
    driver: bridge

volumes:
  redis_dev_data:
    driver: local
  chromadb_dev_data:
    driver: local
