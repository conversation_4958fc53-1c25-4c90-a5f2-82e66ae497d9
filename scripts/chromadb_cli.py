#!/usr/bin/env python3
"""
ChromaDB管理命令行工具
提供数据库管理、备份、恢复等功能
"""

import sys
import argparse
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from services.chromadb_service.management import ChromaDBManager
from shared.utils.logger import setup_logging, get_logger

# 设置日志
setup_logging(level="INFO", service_name="chromadb-cli")
logger = get_logger(__name__)


def cmd_health(args):
    """健康检查命令"""
    manager = ChromaDBManager(args.url)
    
    try:
        result = manager.health_check()
        
        if result["status"] == "healthy":
            print("✅ ChromaDB服务健康")
            print(f"   响应时间: {result['response_time']:.3f}s")
            print(f"   版本: {result.get('version', 'unknown')}")
        else:
            print("❌ ChromaDB服务异常")
            print(f"   错误: {result.get('error', 'unknown')}")
            sys.exit(1)
            
    except Exception as e:
        print(f"❌ 健康检查失败: {e}")
        sys.exit(1)
    finally:
        manager.close()


def cmd_list(args):
    """列出集合命令"""
    manager = ChromaDBManager(args.url)
    
    try:
        collections = manager.list_collections()
        
        if not collections:
            print("📭 没有找到集合")
            return
        
        print(f"📋 发现 {len(collections)} 个集合:")
        print()
        
        for collection in collections:
            name = collection.get("name", "unknown")
            collection_info = manager.get_collection_info(name)
            
            if collection_info:
                doc_count = collection_info.get("document_count", 0)
                metadata = collection_info.get("metadata", {})
                created_at = metadata.get("created_at", "unknown")
                
                print(f"📁 {name}")
                print(f"   文档数量: {doc_count}")
                print(f"   创建时间: {created_at}")
                print()
            else:
                print(f"📁 {name} (无法获取详细信息)")
                print()
                
    except Exception as e:
        print(f"❌ 列出集合失败: {e}")
        sys.exit(1)
    finally:
        manager.close()


def cmd_info(args):
    """集合信息命令"""
    manager = ChromaDBManager(args.url)
    
    try:
        collection_info = manager.get_collection_info(args.collection)
        
        if not collection_info:
            print(f"❌ 集合不存在: {args.collection}")
            sys.exit(1)
        
        print(f"📁 集合信息: {args.collection}")
        print(f"   文档数量: {collection_info.get('document_count', 0)}")
        print(f"   ID: {collection_info.get('id', 'unknown')}")
        
        metadata = collection_info.get("metadata", {})
        if metadata:
            print("   元数据:")
            for key, value in metadata.items():
                print(f"     {key}: {value}")
        
    except Exception as e:
        print(f"❌ 获取集合信息失败: {e}")
        sys.exit(1)
    finally:
        manager.close()


def cmd_delete(args):
    """删除集合命令"""
    if not args.force:
        confirm = input(f"确认删除集合 '{args.collection}'? (y/N): ")
        if confirm.lower() != 'y':
            print("取消删除")
            return
    
    manager = ChromaDBManager(args.url)
    
    try:
        success = manager.delete_collection(args.collection)
        
        if success:
            print(f"✅ 集合删除成功: {args.collection}")
        else:
            print(f"❌ 集合删除失败: {args.collection}")
            sys.exit(1)
            
    except Exception as e:
        print(f"❌ 删除集合失败: {e}")
        sys.exit(1)
    finally:
        manager.close()


def cmd_backup(args):
    """备份命令"""
    manager = ChromaDBManager(args.url)
    
    try:
        success = manager.backup_database(args.backup_dir)
        
        if success:
            print(f"✅ 数据库备份成功: {args.backup_dir}")
        else:
            print(f"❌ 数据库备份失败")
            sys.exit(1)
            
    except Exception as e:
        print(f"❌ 备份失败: {e}")
        sys.exit(1)
    finally:
        manager.close()


def cmd_restore(args):
    """恢复命令"""
    if not args.force:
        confirm = input(f"确认从 '{args.backup_dir}' 恢复数据库? 这将覆盖当前数据! (y/N): ")
        if confirm.lower() != 'y':
            print("取消恢复")
            return
    
    manager = ChromaDBManager(args.url)
    
    try:
        success = manager.restore_database(args.backup_dir)
        
        if success:
            print(f"✅ 数据库恢复成功: {args.backup_dir}")
            print("⚠️  请重启ChromaDB服务以加载恢复的数据")
        else:
            print(f"❌ 数据库恢复失败")
            sys.exit(1)
            
    except Exception as e:
        print(f"❌ 恢复失败: {e}")
        sys.exit(1)
    finally:
        manager.close()


def cmd_stats(args):
    """统计信息命令"""
    manager = ChromaDBManager(args.url)
    
    try:
        stats = manager.get_statistics()
        
        print("📊 ChromaDB统计信息:")
        print()
        print(f"集合总数: {stats.get('total_collections', 0)}")
        print(f"文档总数: {stats.get('total_documents', 0)}")
        
        disk_usage = stats.get('disk_usage', {})
        if disk_usage and 'total_size_mb' in disk_usage:
            print(f"磁盘使用: {disk_usage['total_size_mb']} MB ({disk_usage['file_count']} 个文件)")
        
        service_status = stats.get('service_status', {})
        print(f"服务状态: {service_status.get('status', 'unknown')}")
        
        if args.verbose:
            print()
            print("集合详情:")
            for collection in stats.get('collections_detail', []):
                print(f"  📁 {collection['name']}: {collection['document_count']} 个文档")
        
    except Exception as e:
        print(f"❌ 获取统计信息失败: {e}")
        sys.exit(1)
    finally:
        manager.close()


def cmd_cleanup(args):
    """清理命令"""
    manager = ChromaDBManager(args.url)
    
    try:
        cleaned_count = manager.cleanup_empty_collections()
        print(f"✅ 清理了 {cleaned_count} 个空集合")
        
    except Exception as e:
        print(f"❌ 清理失败: {e}")
        sys.exit(1)
    finally:
        manager.close()


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="ChromaDB管理命令行工具")
    parser.add_argument("--url", default="http://localhost:8001", help="ChromaDB服务URL")
    
    subparsers = parser.add_subparsers(dest="command", help="可用命令")
    
    # 健康检查
    subparsers.add_parser("health", help="检查服务健康状态")
    
    # 列出集合
    subparsers.add_parser("list", help="列出所有集合")
    
    # 集合信息
    info_parser = subparsers.add_parser("info", help="显示集合信息")
    info_parser.add_argument("collection", help="集合名称")
    
    # 删除集合
    delete_parser = subparsers.add_parser("delete", help="删除集合")
    delete_parser.add_argument("collection", help="集合名称")
    delete_parser.add_argument("--force", action="store_true", help="强制删除，不询问确认")
    
    # 备份
    backup_parser = subparsers.add_parser("backup", help="备份数据库")
    backup_parser.add_argument("backup_dir", help="备份目录")
    
    # 恢复
    restore_parser = subparsers.add_parser("restore", help="恢复数据库")
    restore_parser.add_argument("backup_dir", help="备份目录")
    restore_parser.add_argument("--force", action="store_true", help="强制恢复，不询问确认")
    
    # 统计信息
    stats_parser = subparsers.add_parser("stats", help="显示统计信息")
    stats_parser.add_argument("--verbose", "-v", action="store_true", help="显示详细信息")
    
    # 清理
    subparsers.add_parser("cleanup", help="清理空集合")
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    # 执行命令
    command_map = {
        "health": cmd_health,
        "list": cmd_list,
        "info": cmd_info,
        "delete": cmd_delete,
        "backup": cmd_backup,
        "restore": cmd_restore,
        "stats": cmd_stats,
        "cleanup": cmd_cleanup,
    }
    
    command_func = command_map.get(args.command)
    if command_func:
        command_func(args)
    else:
        print(f"❌ 未知命令: {args.command}")
        sys.exit(1)


if __name__ == "__main__":
    main()
