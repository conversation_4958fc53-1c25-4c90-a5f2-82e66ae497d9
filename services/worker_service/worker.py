"""
Worker服务主文件
启动Celery Worker处理向量化和分析任务
"""

import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from shared.utils.logger import setup_logging, get_logger
from shared.celery_config import celery_app
from services.worker_service.config import config

# 导入所有任务模块，确保任务被注册
from services.worker_service.tasks import (
    vectorize_file,
    process_documents,
    analyze_risk,
    health_check
)

# 设置日志
setup_logging(level=config.log_level, service_name=config.service_name)

logger = get_logger(__name__)


def main():
    """Worker服务主函数"""
    logger.info("=" * 60)
    logger.info(f"🚀 启动 {config.service_name} v{config.service_version}")
    logger.info("=" * 60)
    
    # 服务配置信息
    logger.info(f"📝 日志级别: {config.log_level}")
    
    # BGE-M3模型配置
    logger.info(f"🤖 BGE-M3模型路径: {config.model_name}")
    logger.info(f"🔧 设备: {config.device}")
    logger.info(f"📊 批次大小: {config.batch_size}")
    logger.info(f"📏 最大长度: {config.max_length}")
    
    # 依赖服务配置
    logger.info(f"🗄️  ChromaDB: {config.chroma_url}")
    logger.info(f"🔴 Redis: {config.redis_url}")
    logger.info(f"🤖 LLM提供商: {config.llm_provider}")
    
    # 任务配置
    logger.info(f"⏱️  任务超时: {config.task_timeout}秒")
    logger.info(f"🔄 最大重试次数: {config.max_retries}")
    logger.info(f"📦 分块大小: {config.chunk_size}")
    logger.info(f"🔗 分块重叠: {config.chunk_overlap}")
    
    # 内存管理配置
    logger.info(f"🧠 内存监控: {config.enable_memory_monitoring}")
    logger.info(f"🧹 自动清理: {config.auto_cleanup}")
    logger.info(f"⚠️  内存清理阈值: {config.memory_cleanup_threshold}%")
    
    logger.info("✅ Worker服务配置完成")
    logger.info("=" * 60)
    
    # 显示注册的任务
    logger.info("📋 已注册的任务:")
    for task_name in celery_app.tasks:
        if not task_name.startswith('celery.'):
            logger.info(f"  - {task_name}")
    
    logger.info("=" * 60)
    logger.info("🎯 Worker服务已准备就绪，等待任务...")
    logger.info("💡 使用以下命令启动Worker:")
    logger.info("   celery -A services.worker_service.worker worker --loglevel=info")
    logger.info("=" * 60)


if __name__ == "__main__":
    main()
    
    # 如果直接运行此文件，启动Worker
    # 注意：生产环境建议使用celery命令启动
    import subprocess
    
    cmd = [
        "celery", "-A", "services.worker_service.worker", "worker",
        "--loglevel", config.log_level.lower(),
        "--concurrency", "1",  # 由于BGE-M3模型占用较多资源，建议单进程
        "--prefetch-multiplier", "1",  # 每次只处理一个任务
        "--max-tasks-per-child", "10",  # 每个worker处理10个任务后重启
    ]
    
    try:
        subprocess.run(cmd)
    except KeyboardInterrupt:
        logger.info("Worker服务已停止")
    except Exception as e:
        logger.error(f"Worker服务启动失败: {e}")
